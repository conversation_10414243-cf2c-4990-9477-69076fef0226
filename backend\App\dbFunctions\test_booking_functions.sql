-- Test script for booking functionality
-- This is a test script to verify the booking functions work correctly

-- Test 1: Test tms_get_available_count function
SELECT 'Testing tms_get_available_count function' as test_name;

-- Test with null parameters (should return 0)
SELECT tms_get_available_count(null, null) as result_null_params;

-- Test 2: Test tms_create_booking_data function with missing parameters
SELECT 'Testing tms_create_booking_data with missing parameters' as test_name;

-- Test with null srvc_req_id (should return error)
SELECT tms_create_booking_data(null, '{"capacity_id": 1, "org_id": 1, "usr_id": "123e4567-e89b-12d3-a456-426614174000", "ip_address": "127.0.0.1", "user_agent": "test"}') as result_null_srvc_req;

-- Test with null capacity_id (should return error)
SELECT tms_create_booking_data(1, '{"org_id": 1, "usr_id": "123e4567-e89b-12d3-a456-426614174000", "ip_address": "127.0.0.1", "user_agent": "test"}') as result_null_capacity;

-- Test 3: Test service request creation with booking validation
SELECT 'Testing service request creation with booking validation' as test_name;

-- This would test the validation logic in tms_create_service_request
-- Note: This is just a demonstration of how the validation would work
-- Actual testing would require proper test data setup

SELECT 'Booking functionality tests completed' as test_status;
