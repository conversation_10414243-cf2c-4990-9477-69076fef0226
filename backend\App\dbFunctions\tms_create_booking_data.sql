CREATE OR REPLACE FUNCTION public.tms_create_booking_data(
    srvc_req_id_ bigint,
    form_data_ json
)
RETURNS json
LANGUAGE plpgsql
AS $function$
declare
    status boolean default false;
    message text default 'Internal error';
    resp_data json;
    
    -- Form data variables
    capacity_id_ bigint;
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    
    -- Booking logic variables
    manpower_ integer;
    job_duration_ integer; -- in minutes
    hub_travel_time_ integer default 0; -- in minutes
    total_duration_ integer; -- job_duration + hub_travel_time
    man_minutes_ integer; -- total_duration * manpower
    
    -- Capacity variables
    available_count_ integer;
    slot_duration_ integer; -- in minutes
    total_cap_in_minutes_ integer;
    available_cap_in_minutes_ integer;
    
    -- Booking variables
    booking_ids_ bigint[];
    booking_id_ bigint;
    affected_rows integer;
    
    -- Timeline variables
    srvc_type_id_ integer;
    timeline_resp json;
    
begin
    -- Extract form data
    capacity_id_ = (form_data_->>'capacity_id')::bigint;
    org_id_ = (form_data_->>'org_id')::integer;
    usr_id_ = (form_data_->>'usr_id')::uuid;
    ip_address_ = form_data_->>'ip_address';
    user_agent_ = form_data_->>'user_agent';
    
    -- Validate required parameters
    if srvc_req_id_ is null then
        return json_build_object('status', false, 'message', 'Service request ID is required');
    end if;
    
    if capacity_id_ is null then
        return json_build_object('status', false, 'message', 'Capacity ID is required');
    end if;
    
    -- Get booking parameters from form_data or set defaults
    manpower_ = coalesce((form_data_->>'manpower')::integer, 1);
    job_duration_ = coalesce((form_data_->>'job_duration')::integer, 60); -- default 1 hour
    hub_travel_time_ = coalesce((form_data_->>'hub_travel_time')::integer, 0);
    
    -- Calculate total duration and man minutes
    total_duration_ = job_duration_ + hub_travel_time_;
    man_minutes_ = total_duration_ * manpower_;
    
    -- Get available capacity count using helper function
    available_count_ = tms_get_available_count(null, capacity_id_);

    -- Get slot details
    select
        total_capacity,
        extract(epoch from (end_time - start_time))::integer / 60 as slot_duration_minutes,
        (extract(epoch from (end_time - start_time))::integer / 60) * available_capacity as available_cap_minutes
    into
        total_cap_in_minutes_,
        slot_duration_,
        available_cap_in_minutes_
    from cl_tx_capacity
    where db_id = capacity_id_;

    if not found then
        return json_build_object('status', false, 'message', 'Capacity record not found');
    end if;
    
    -- Booking logic
    if total_duration_ > slot_duration_ then
        -- Book capacity across slots (not implemented in this version)
        return json_build_object('status', false, 'message', 'Cross-slot booking not yet implemented');
    else
        -- Book capacity within the slot
        if manpower_ <= available_count_ then
            -- Check if man minutes are available
            if man_minutes_ <= available_cap_in_minutes_ then
                -- Proceed with booking
                
                -- Insert booking record
                insert into cl_tx_bookings (
                    capacity_id,
                    order_id,
                    order_label,
                    booked_qty,
                    c_meta
                ) values (
                    capacity_id_,
                    srvc_req_id_,
                    'Service Request Booking',
                    man_minutes_,
                    row(ip_address_, user_agent_, now() at time zone 'utc')
                ) returning db_id into booking_id_;
                
                get diagnostics affected_rows = ROW_COUNT;
                
                if affected_rows > 0 then
                    -- Update capacity table with booked minutes
                    update cl_tx_capacity 
                    set 
                        booked_cap_in_minutes = coalesce(booked_cap_in_minutes, 0) + man_minutes_,
                        u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
                    where db_id = capacity_id_;
                    
                    -- Add to booking_ids array
                    booking_ids_ = array_append(booking_ids_, booking_id_);
                    
                    -- Add timeline entry
                    select srvc_type_id into srvc_type_id_ 
                    from cl_tx_srvc_req 
                    where db_id = srvc_req_id_;
                    
                    if srvc_type_id_ is not null then
                        timeline_resp = tms_add_to_srvc_req_timeline(
                            srvc_type_id_, 
                            srvc_req_id_, 
                            'UPDATE', 
                            'Booking slots done', 
                            json_build_object(
                                'booking_id', booking_id_,
                                'capacity_id', capacity_id_,
                                'man_minutes', man_minutes_,
                                'manpower', manpower_,
                                'duration', total_duration_
                            )
                        );
                    end if;
                    
                    status = true;
                    message = 'success';
                    resp_data = json_build_object(
                        'booking_ids', booking_ids_,
                        'booking_details', json_build_object(
                            'capacity_id', capacity_id_,
                            'manpower', manpower_,
                            'job_duration', job_duration_,
                            'hub_travel_time', hub_travel_time_,
                            'total_duration', total_duration_,
                            'man_minutes', man_minutes_,
                            'booking_id', booking_id_
                        )
                    );
                else
                    return json_build_object('status', false, 'message', 'Failed to create booking record');
                end if;
            else
                return json_build_object('status', false, 'message', 'Insufficient capacity available. Required: ' || man_minutes_ || ' minutes, Available: ' || available_cap_in_minutes_ || ' minutes');
            end if;
        else
            return json_build_object('status', false, 'message', 'Insufficient manpower available. Required: ' || manpower_ || ', Available: ' || available_count_);
        end if;
    end if;
    
    return json_build_object('status', status, 'message', message, 'data', resp_data);
    
exception when others then
    return json_build_object(
        'status', false,
        'message', 'Error creating booking data: ' || SQLERRM
    );
end;
$function$;
