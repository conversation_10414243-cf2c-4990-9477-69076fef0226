CREATE OR REPLACE FUNCTION public.tms_get_available_count(
    resource_id_ text DEFAULT NULL,
    capacity_id_ bigint DEFAULT NULL
)
RETURNS integer
LANGUAGE plpgsql
AS $function$
declare
    available_count integer default 0;
begin
    -- Get available capacity count based on resourceId or capacity_id
    if capacity_id_ is not null then
        -- Get available count by capacity_id
        select available_capacity 
        into available_count
        from cl_tx_capacity 
        where db_id = capacity_id_;
        
    elsif resource_id_ is not null then
        -- Get available count by resource_id (sum of all slots for the resource)
        select coalesce(sum(available_capacity), 0)
        into available_count
        from cl_tx_capacity 
        where resource_id = resource_id_
        and usr_tmzone_day >= current_date;
        
    else
        -- No valid parameters provided
        return 0;
    end if;
    
    return coalesce(available_count, 0);
    
exception when others then
    return 0;
end;
$function$;
